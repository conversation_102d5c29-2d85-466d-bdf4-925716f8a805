package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 明细导出DTO基类
 *
 * 包含社保明细和个税明细导出的共同字段，用于优雅抽象和代码复用。
 * 共同字段包括：方式、姓名、身份证号、手机号、备注、社保基数
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("明细导出DTO基类")
public abstract class BaseDetailExportDTO {

    /** 方式 */
    @Excel(name = "方式", sort = 1)
    @ApiModelProperty(value = "方式")
    private String operationType;

    /** 姓名 */
    @Excel(name = "姓名", sort = 2)
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /** 身份证号 */
    @Excel(name = "身份证号", sort = 3)
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @Excel(name = "手机号", sort = 4)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 备注 */
    @Excel(name = "备注", sort = 5)
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 社保基数 */
    @Excel(name = "社保基数", sort = 6)
    @ApiModelProperty(value = "社保基数")
    private String socialInsuranceBase;

    /** 养老 */
    @Excel(name = "养老", sort = 7)
    @ApiModelProperty(value = "养老")
    private String yangLao;

    /** 失业 */
    @Excel(name = "失业", sort = 8)
    @ApiModelProperty(value = "失业")
    private String shiYe;

    /** 工伤 */
    @Excel(name = "工伤", sort = 9)
    @ApiModelProperty(value = "工伤")
    private String gongShang;

    /** 医疗 */
    @Excel(name = "医疗", sort = 10)
    @ApiModelProperty(value = "医疗")
    private String yiLiao;

    /** 生育 */
    @Excel(name = "生育", sort = 11)
    @ApiModelProperty(value = "生育")
    private String shengYu;

    /** 其他 */
    @Excel(name = "其他", sort = 12)
    @ApiModelProperty(value = "其他保险")
    private String qiTa;
}
